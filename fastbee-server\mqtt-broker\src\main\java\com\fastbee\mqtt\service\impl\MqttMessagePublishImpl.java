package com.fastbee.mqtt.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.fastbee.common.core.mq.DeviceReport;
import com.fastbee.common.core.mq.DeviceReportBo;
import com.fastbee.common.core.mq.MQSendMessageBo;
import com.fastbee.common.core.mq.message.DeviceData;
import com.fastbee.common.core.mq.message.DeviceDownMessage;
import com.fastbee.common.core.mq.message.InstructionsMessage;
import com.fastbee.common.core.mq.message.ShadowMessage;
import com.fastbee.common.utils.UInt32IdGenerator;
import com.fastbee.common.core.mq.message.MqttBo;
import com.fastbee.common.core.mq.ota.OtaUpgradeBo;
import com.fastbee.common.core.protocol.Message;
import com.fastbee.common.core.protocol.modbus.ModbusCode;
import com.fastbee.common.core.redis.RedisCache;
import com.fastbee.common.core.redis.RedisKeyBuilder;
import com.fastbee.common.core.thingsModel.ThingsModelSimpleItem;
import com.fastbee.common.core.thingsModel.ThingsModelValuesInput;
import com.fastbee.common.enums.FunctionReplyStatus;
import com.fastbee.common.enums.OTAUpgrade;
import com.fastbee.common.enums.ServerType;
import com.fastbee.common.enums.TopicType;
import com.fastbee.common.exception.ServiceException;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.common.utils.StringUtils;
import com.fastbee.common.utils.gateway.CRC16Utils;
import com.fastbee.common.utils.gateway.mq.TopicsUtils;
import com.fastbee.common.utils.ip.IpUtils;
import com.fastbee.iot.domain.Device;
import com.fastbee.iot.domain.FunctionLog;
import com.fastbee.iot.domain.Product;
import com.fastbee.iot.domain.SimulateLog;
import com.fastbee.iot.domain.ThingsModel;
import com.fastbee.iot.model.NtpModel;
import com.fastbee.iot.model.ThingsModels.PropertyDto;
import com.fastbee.iot.model.ThingsModels.ValueItem;
import com.fastbee.iot.ruleEngine.MsgContext;
import com.fastbee.iot.ruleEngine.RuleProcess;
import com.fastbee.iot.service.*;
import com.fastbee.iot.service.cache.IFirmwareCache;
import com.fastbee.iot.util.SnowflakeIdWorker;
import com.fastbee.modbus.codec.ModbusMessageDecoder;
import com.fastbee.modbus.codec.ModbusMessageEncoder;
import com.fastbee.modbus.model.ModbusRtu;
import com.fastbee.mq.model.ReportDataBo;
import com.fastbee.mq.service.IDataHandler;
import com.fastbee.mq.service.IDeviceReportMessageService;
import com.fastbee.mq.service.IMqttMessagePublish;
import com.fastbee.mq.service.impl.MessageManager;
import com.fastbee.mqtt.manager.MqttRemoteManager;
import com.fastbee.mqtt.model.PushMessageBo;
import com.fastbee.mqttclient.PubMqttClient;
import com.fastbee.protocol.base.protocol.IProtocol;
import com.fastbee.protocol.domain.DeviceProtocol;
import com.fastbee.protocol.service.IProtocolManagerService;
import com.fastbee.sip.service.IGatewayService;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 消息推送方法集合
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttMessagePublishImpl implements IMqttMessagePublish {


    @Resource
    private IProductService productService;
    @Resource
    private IProtocolManagerService protocolManagerService;
    @Resource
    private PubMqttClient mqttClient;
    @Resource
    private IFirmwareCache firmwareCache;
    @Resource
    private IFirmwareTaskDetailService firmwareTaskDetailService;
    @Resource
    private MessageManager messageManager;
    @Resource
    private TopicsUtils topicsUtils;
    @Resource
    private IDeviceService deviceService;
    @Resource
    private ISimulateLogService simulateLogService;
    @Resource
    private MqttRemoteManager remoteManager;
    @Resource
    private RedisCache redisCache;
    @Resource
    private IDeviceReportMessageService reportMessageService;
    @Resource
    private IFunctionLogService functionLogService;
    @Resource
    private IDataHandler dataHandler;
    @Resource
    private IThingsModelService thingsModelService;
    @Resource
    private RuleProcess ruleProcess;

    @Autowired
    private IGatewayService gatewayService;

    private final SnowflakeIdWorker snowflakeIdWorker = new SnowflakeIdWorker(3);

    private static final ModbusMessageDecoder decoder = new ModbusMessageDecoder("com.fastbee.modbus");
    private static final ModbusMessageEncoder encoder = new ModbusMessageEncoder("com.fastbee.modbus");

    @Override
    public InstructionsMessage buildMessage(DeviceDownMessage downMessage, TopicType type) {
        /*返回的组将数据*/
        InstructionsMessage message = new InstructionsMessage();
        /*根据设备编号查询产品信息*/
        if (StringUtils.isEmpty(downMessage.getProtocolCode())) {
            Product product = productService.getProductBySerialNumber(downMessage.getSerialNumber());
            Optional.ofNullable(product).orElseThrow(() -> new ServiceException("产品为空"));
            downMessage.setProtocolCode(product.getProtocolCode());
        }
        String serialNumber = downMessage.getSerialNumber() == null ? "" : downMessage.getSerialNumber();

        /*组建Topic*/
        String topicName = "";
        if (downMessage.getServerType().equals(ServerType.MQTT)) {
            topicName = topicsUtils.buildTopic(downMessage.getProductId(), serialNumber, type);
        }
        /*获取编码协议*/
        IProtocol protocolInstance = protocolManagerService.getProtocolByProtocolCode(downMessage.getProtocolCode());
        DeviceData encodeData = DeviceData.builder()
                .downMessage(downMessage)
                .serialNumber(serialNumber)
                .body(downMessage.getBody())
                .code(downMessage.getCode())
                .topicName(topicName).build();
        //根据协议编码后数据
        byte[] data = protocolInstance.encode(encodeData, null);
        if (data == null) {
            log.error("=>协议编码失败,设备编号={},协议代码={}", serialNumber, downMessage.getProtocolCode());
            throw new ServiceException("协议编码失败,设备编号:" + serialNumber);
        }
        message.setMessage(data);
        message.setSerialNumber(serialNumber);
        message.setTopicName(topicName);

        return message;
    }

    /**
     * 服务(指令)下发
     */
    @Override
    public void funcSend(MQSendMessageBo bo) {
        //如果协议编号为空，则获取
        if (StringUtils.isEmpty(bo.getProtocolCode())) {
            Product product = productService.selectProductByProductId(bo.getProductId());
            //bo.setType(ThingsModelType.SERVICE);
            bo.setProtocolCode(product.getProtocolCode());
            bo.setTransport(product.getTransport());
        }

        //处理设备影子模式 - 使用新的数据结构格式
        if (null != bo.getIsShadow() && bo.getIsShadow()){
            // 构建新格式的影子消息
            ShadowMessage shadowMessage = ShadowMessage.builder()
                    .id(UInt32IdGenerator.generateTimeBasedId()) // 使用32位无符号整数ID
                    .deviceId(bo.getSerialNumber())  // 设备序列号作为设备ID
                    .data(new HashMap<>())
                    .build();

            // 将键值对数据添加到data字段中
            bo.getValue().forEach((key, value) -> {
                shadowMessage.getData().put(key, value);
            });

            // 发布影子消息到MQTT
            String topic = topicsUtils.buildTopic(bo.getProductId(), bo.getSerialNumber(), TopicType.FUNCTION_GET);
            String message = JSON.toJSONString(shadowMessage);
            mqttClient.publish(1, true, topic, message);

            // 同时保持原有的数据处理逻辑（用于内部处理）
            List<ThingsModelSimpleItem> dataList = new ArrayList<>();
            bo.getValue().forEach((key,value) ->{
                ThingsModelSimpleItem item = new ThingsModelSimpleItem();
                item.setId(key);
                item.setValue(value+"");
                dataList.add(item);
            });
            ReportDataBo dataBo = new ReportDataBo();
            dataBo.setDataList(dataList);
            dataBo.setProductId(bo.getProductId());
            dataBo.setSerialNumber(bo.getSerialNumber());
            dataBo.setRuleEngine(false);
            dataBo.setShadow(true);
            dataBo.setSlaveId(bo.getSlaveId());
            dataBo.setType(bo.getType().getCode());
            dataHandler.reportData(dataBo);
            return;
        }

        /* 下发服务数据存储对象*/
        FunctionLog funcLog = new FunctionLog();
        funcLog.setCreateTime(DateUtils.getNowDate());
        funcLog.setFunValue(bo.getValue().get(bo.getIdentifier()).toString());
        funcLog.setMessageId(bo.getMessageId());
        funcLog.setSerialNumber(bo.getSerialNumber());
        funcLog.setIdentify(bo.getIdentifier());
        funcLog.setShowValue(bo.getShowValue());
        funcLog.setFunType(1);
        funcLog.setModelName(bo.getModelName());
        //兼容子设备
        if (null != bo.getSlaveId()) {
            PropertyDto thingModels = thingsModelService.getSingleThingModels(bo.getProductId(), bo.getIdentifier() + "#" + bo.getSlaveId());
            funcLog.setSerialNumber(bo.getSerialNumber() + "_" + bo.getSlaveId());
            //log.setSerialNumber(bo.getSerialNumber() + "_" + bo.getSlaveId());
            bo.setCode(ModbusCode.Write06);
            if (!Objects.isNull(thingModels.getCode())){
                bo.setCode(ModbusCode.getInstance(Integer.parseInt(thingModels.getCode())));
            }
        }

        ServerType serverType = ServerType.explain(bo.getTransport());
        Optional.ofNullable(serverType).orElseThrow(() -> new ServiceException("产品的传输协议编码为空!"));

        // 查询物模型获取PLC ID
        String plcId = null;
        try {
            ThingsModel queryModel = new ThingsModel();
            queryModel.setProductId(bo.getProductId());
            queryModel.setIdentifier(bo.getIdentifier());
            ThingsModel model = thingsModelService.selectSingleThingsModel(queryModel);
            if (model != null && StringUtils.isNotEmpty(model.getPlcIds())) {
                plcId = model.getPlcIds();
                log.debug("获取到物模型PLC ID: identifier={}, plcId={}", bo.getIdentifier(), plcId);
            }
        } catch (Exception e) {
            log.error("查询物模型PLC ID失败: identifier={}, error={}", bo.getIdentifier(), e.getMessage());
        }

        /*下发服务数据处理对象*/
        DeviceDownMessage downMessage = DeviceDownMessage.builder()
                .messageId(bo.getMessageId())
                .body(bo.getValue())
                .serialNumber(bo.getSerialNumber())
                .productId(bo.getProductId())
                .timestamp(DateUtils.getTimestamp())
                .identifier(bo.getIdentifier())
                .slaveId(bo.getSlaveId())
                .code(bo.getCode() == ModbusCode.Read01 ? ModbusCode.Write05 : ModbusCode.Write06)
                .serverType(serverType)
                .plcId(plcId)
                .build();
        switch (serverType) {
            case MQTT:
                //组建下发服务指令
                InstructionsMessage instruction = buildMessage(downMessage, TopicType.FUNCTION_GET);

                //  规则引擎脚本处理,完成后返回结果
                // 检查指令消息是否为空，避免空指针异常
                if (instruction.getMessage() == null) {
                    log.error("=>指令编码失败,设备编号={},指令数据为空", bo.getSerialNumber());
                    funcLog.setResultMsg("指令编码失败,指令数据为空");
                    functionLogService.insertFunctionLog(funcLog);
                    return;
                }
                MsgContext context = ruleProcess.processRuleScript(bo.getSerialNumber(), 2,instruction.getTopicName(),new String(instruction.getMessage()));
                if (!Objects.isNull(context) && StringUtils.isNotEmpty(context.getPayload())
                        && StringUtils.isNotEmpty(context.getTopic())) {
                    instruction.setTopicName(context.getTopic());
                    instruction.setMessage(context.getPayload().getBytes());
                }

                mqttClient.publish(instruction.getTopicName(), instruction.getMessage(), funcLog);
                log.debug("=>服务下发,topic=[{}],指令=[{}]", instruction.getTopicName(),
                    instruction.getMessage() != null ? new String(instruction.getMessage()) : "null");
                break;
            case TCP:
                InstructionsMessage message = buildMessage(downMessage, null);
                if (message.getMessage() == null) {
                    log.error("=>TCP指令编码失败,设备编号={},指令数据为空", bo.getSerialNumber());
                    funcLog.setResultMsg("TCP指令编码失败,指令数据为空");
                    functionLogService.insertFunctionLog(funcLog);
                    return;
                }
                Message data = new Message();
                data.setPayload(Unpooled.wrappedBuffer(message.getMessage()));
                data.setClientId(message.getSerialNumber());
                messageManager.requestR(bo.getSerialNumber(), data, Message.class);
                funcLog.setResultMsg(FunctionReplyStatus.NORELY.getMessage());
                funcLog.setResultCode(FunctionReplyStatus.NORELY.getCode());
                functionLogService.insertFunctionLog(funcLog);
                break;
            case UDP:
                break;
            case COAP:
                break;
            case GB28181:
                MqttMessagePublishImpl.log.debug("=>功能指令下发,functinos=[{}]", bo);
                gatewayService.sendFunction(bo.getSerialNumber(),bo.getIdentifier(),bo.getValue().getString(bo.getIdentifier()));
                break;
            case WEBSOCKET:
            case WX_WEBSOCKET:
            case OTHER:
                log.warn("=>暂不支持的服务类型: {}", serverType);
                break;
        }
    }

    /**
     * OTA升级下发
     *
     * @param bo
     */
    @Override
    public void upGradeOTA(OtaUpgradeBo bo) {
        //获取唯一messageId
        bo.setMessageId(String.valueOf(snowflakeIdWorker.nextId()));
        //获取固件版本缓存
        String url = this.firmwareCache.getFirmwareCache(bo.getOtaId());
        if (StringUtils.isEmpty(url)) {
            firmwareCache.setFirmwareCache(bo.getOtaId(), bo.getOtaUrl());
        }
        DeviceProtocol deviceProtocol = protocolManagerService.getProtocolBySerialNumber(bo.getSerialNumber());
        IProtocol protocol = deviceProtocol.getProtocol();
        /*组建下发OTA升级topic*/
        String topicName = topicsUtils.buildTopic(bo.getProductId(), bo.getSerialNumber(), TopicType.FIRMWARE_SET);
        DeviceDownMessage deviceDownMessage = buildMessage(bo);
        DeviceData deviceSource = DeviceData.builder()
                .serialNumber(bo.getSerialNumber())
                .topicName(topicName)
                .downMessage(deviceDownMessage)
                .build();
        // 编码OTA升级消息
        byte[] otaUpgrade = protocol.encode(deviceSource, null);
        FunctionLog log = new FunctionLog();
        log.setCreateTime(DateUtils.getNowDate());
        log.setSerialNumber(bo.getSerialNumber());
        log.setFunType(3);
        log.setMessageId(bo.getMessageId());
        log.setDeviceName(bo.getDeviceName());
        log.setIdentify("OTA");
        log.setFunValue("名称:" + bo.getFirmwareName() + " 版本:" + bo.getFirmwareVersion());
        log.setShowValue(bo.getOtaUrl());
        // 通过内部mqtt客户端下发消息
        mqttClient.publish(topicName, otaUpgrade, log);

        //    更新数据库
        firmwareTaskDetailService.update(bo, OTAUpgrade.SEND);

    }

    @Override
    public void sendFunctionMessage(DeviceReportBo bo) {
        log.warn("=>功能指令下发,sendFunctionMessage bo=[{}]", bo);
        Device device = deviceService.selectDeviceBySerialNumber(bo.getSerialNumber());
        Optional.ofNullable(device).orElseThrow(()->new ServiceException("服务下发的设备:["+bo.getSerialNumber()+"]不存在"));

        Product product = productService.selectProductByProductId(topicsUtils.parseProductId(bo.getTopicName()));
        ServerType serverType = ServerType.explain(product.getTransport());
        Optional.ofNullable(serverType).orElseThrow(() -> new ServiceException("产品的传输协议编码为空!"));

        switch (serverType) {
            case GB28181:
                List<ThingsModelSimpleItem> functinos = JSON.parseArray(bo.getData(),ThingsModelSimpleItem.class);
                log.debug("=>功能指令下发,functinos=[{}]", functinos.toString());
                gatewayService.sendFunction(bo.getSerialNumber(),functinos);
                break;
            case MQTT:
            case TCP:
            case UDP:
            case COAP:
            case WEBSOCKET:
            case WX_WEBSOCKET:
            case OTHER:
                log.warn("=>sendFunctionMessage暂不支持的服务类型: {}", serverType);
                break;
        }
    }

    /**
     * 1.发布设备状态
     */
    @Override
    public void publishStatus(Long productId, String deviceNum, int deviceStatus, int isShadow, int rssi) {
        String message = "{\"status\":" + deviceStatus + ",\"isShadow\":" + isShadow + ",\"rssi\":" + rssi + "}";
        String topic = topicsUtils.buildTopic(productId, deviceNum, TopicType.STATUS_POST);
        mqttClient.publish(1, false, topic, message);
    }


    /**
     * 2.发布设备信息
     */
    @Override
    public void publishInfo(Long productId, String deviceNum) {
        String topic = topicsUtils.buildTopic(productId, deviceNum, TopicType.INFO_GET);
        mqttClient.publish(1, false, topic, "");
    }

    /**
     * 3.发布时钟同步信息
     *
     * @param bo 数据模型
     */
    public void publishNtp(ReportDataBo bo) {
        NtpModel ntpModel = JSON.parseObject(bo.getMessage(), NtpModel.class);
        ntpModel.setServerRecvTime(System.currentTimeMillis());
        ntpModel.setServerSendTime(System.currentTimeMillis());
        String topic = topicsUtils.buildTopic(bo.getProductId(), bo.getSerialNumber(), TopicType.NTP_GET);
        mqttClient.publish(1, false, topic, JSON.toJSONString(ntpModel));
    }

    /**
     * 4.发布属性 - 使用新的影子消息格式
     * delay 延时，秒为单位
     */
    @Override
    public void publishProperty(Long productId, String deviceNum, List<ThingsModelSimpleItem> thingsList, int delay) {
        String pre = "";
        if (delay > 0) {
            pre = "$delayed/" + String.valueOf(delay) + "/";
        }
        String topic = pre + topicsUtils.buildTopic(productId, deviceNum, TopicType.FUNCTION_GET);

        if (thingsList == null) {
            mqttClient.publish(1, true, topic, "");
        } else {
            // 构建新格式的影子消息
            ShadowMessage shadowMessage = ShadowMessage.builder()
                    .id(UInt32IdGenerator.generateTimeBasedId())
                    .deviceId(deviceNum)
                    .data(new HashMap<>())
                    .build();

            // 将ThingsModelSimpleItem列表转换为键值对
            for (ThingsModelSimpleItem item : thingsList) {
                shadowMessage.getData().put(item.getId(), item.getValue());
            }

            mqttClient.publish(1, true, topic, JSON.toJSONString(shadowMessage));
        }
    }

    /**
     * 5.发布功能 - 使用新的影子消息格式
     * delay 延时，秒为单位
     */
    @Override
    public void publishFunction(Long productId, String deviceNum, List<ThingsModelSimpleItem> thingsList, int delay) {
        String pre = "";
        if (delay > 0) {
            pre = "$delayed/" + String.valueOf(delay) + "/";
        }
        String topic = pre + topicsUtils.buildTopic(productId, deviceNum, TopicType.FUNCTION_GET);

        if (thingsList == null) {
            mqttClient.publish(1, true, topic, "");
        } else {
            // 构建新格式的影子消息
            ShadowMessage shadowMessage = ShadowMessage.builder()
                    .id(UInt32IdGenerator.generateTimeBasedId())
                    .deviceId(deviceNum)
                    .data(new HashMap<>())
                    .build();

            // 将ThingsModelSimpleItem列表转换为键值对
            for (ThingsModelSimpleItem item : thingsList) {
                shadowMessage.getData().put(item.getId(), item.getValue());
            }

            mqttClient.publish(1, true, topic, JSON.toJSONString(shadowMessage));
        }
    }

    /**
     * 设备数据同步
     *
     * @param deviceNumber 设备编号
     * @return 设备
     */
    public Device deviceSynchronization(String deviceNumber) {
        Device device = deviceService.selectDeviceBySerialNumber(deviceNumber);
        // 1-未激活，2-禁用，3-在线，4-离线
        if (device.getStatus() == 3) {
            device.setStatus(4);
            deviceService.updateDeviceStatus(device);
            // 发布设备信息
            publishInfo(device.getProductId(), device.getSerialNumber());
        }
        return device;
    }

    /**
     * 用于发送模拟客户端数据
     */
    @Override
    public void sendSimulatorMessage(String topic,byte[] source){
        String serialNumber = topicsUtils.parseSerialNumber(topic);
        if (serialNumber.contains("_")){
            serialNumber = serialNumber.split("_")[0];
        }
        Long productId = topicsUtils.parseProductId(topic);
        DeviceData data = DeviceData.builder()
                .serialNumber(serialNumber)
                .topicName(topic)
                .data(source)
                .isEnabledTest(true)
                .buf(Unpooled.wrappedBuffer(source))
                .build();
        //获取下发的读指令
        ModbusRtu rtu = decoder.decode(data);
        /*根据数据个数，拼装随机数,模拟modbus设备上报的数据*/
        short[] bytes = new short[rtu.getCount()];
        Random random = new Random();

        DeviceReport report = new DeviceReport();
        ThingsModelValuesInput modelValuesInput = new ThingsModelValuesInput();
        List<ThingsModelSimpleItem> values = new ArrayList<>();
        for (int i = 0; i < rtu.getCount(); i++) {
            int nextInt = random.nextInt(100);
            bytes[i] = (short) nextInt;
            ThingsModelSimpleItem things = new ThingsModelSimpleItem();
            things.setId(rtu.getAddress()+i+"");
            things.setValue(nextInt + "");
            things.setTs(DateUtils.getNowDate());
            things.setSlaveId(rtu.getSlaveId());
            values.add(things);
        }
        modelValuesInput.setThingsModelValueRemarkItem(values);
        report.setSlaveId(rtu.getSlaveId());
        report.setValuesInput(modelValuesInput);
        report.setSerialNumber(serialNumber);
        report.setProductId(productId);
        report.setServerType(ServerType.MQTT);
        String upTopic = topicsUtils.buildTopic(productId, serialNumber, TopicType.PROPERTY_POST_SIMULATE);
        reportMessageService.handlerReportMessage(report,upTopic);

        rtu.setData(bytes);
        rtu.setEnableTest(true);
        ByteBuf buf = encoder.encode(rtu);
        byte[] write = new byte[buf.writerIndex()];
        buf.readBytes(write);
        byte[] result = CRC(write);
        ReferenceCountUtil.release(buf);
        log.debug("<<<<<<<<<<模拟客户端推送主题[{}],消息[{}]",upTopic,ByteBufUtil.hexDump(result));


        MqttBo send = new MqttBo();
        send.setData(ByteBufUtil.hexDump(source));
        send.setTopic(topic);
        MqttBo receive = new MqttBo();
        receive.setTopic(upTopic);
        receive.setData(ByteBufUtil.hexDump(result));
        updateSimulationLog(send,receive,serialNumber);
        //发送至WS
        String pushTopic = topicsUtils.buildTopic(productId, serialNumber, TopicType.WS_SERVICE_INVOKE_SIMULATE);
        sendSimulationWs(send,receive,pushTopic);
    }

    /**
     * 模拟设备写客户端数据
     * @param topic
     */
    @Override
    public void getSimulatorInfo(String topic,byte[] source){
        String serialNumber = topicsUtils.parseSerialNumber(topic);
        Long productId = topicsUtils.parseProductId(topic);
        DeviceData data = DeviceData.builder()
                .serialNumber(serialNumber)
                .topicName(topic)
                .data(source)
                .code(ModbusCode.Write06)
                .buf(Unpooled.wrappedBuffer(source))
                .build();
        //获取下发的写指令
        ModbusRtu rtu = decoder.decode(data);
        //更新redis的值
        String cacheKey = RedisKeyBuilder.buildTSLVCacheKey(productId, serialNumber);
        String hkey = rtu.getAddress() + "#" + rtu.getSlaveId();
        ValueItem modbusItem = redisCache.getCacheMapValue(cacheKey, hkey);
        String s1 = rtu.getAddress() + "";
        if (null == modbusItem){
            modbusItem = new ValueItem(s1, rtu.getSlaveId(),s1);
        }
        modbusItem.setShadow(rtu.getWriteData()+"");
        modbusItem.setValue(rtu.getWriteData()+"");
        modbusItem.setTs(DateUtils.getNowDate());
        redisCache.setCacheMapValue(cacheKey,hkey, com.alibaba.fastjson2.JSONObject.toJSONString(modbusItem));

        /*将最新值推送到前端*/
        List<ThingsModelSimpleItem> result = new ArrayList<>();
        ThingsModelSimpleItem item = new ThingsModelSimpleItem();
        item.setId(s1);
        item.setValue(rtu.getWriteData()+"");
        item.setTs(DateUtils.getNowDate());
        item.setSlaveId(rtu.getSlaveId());
        result.add(item);
        PushMessageBo push = new PushMessageBo();
        push.setTopic(topicsUtils.buildTopic(productId, serialNumber, TopicType.WS_SERVICE_INVOKE));
        push.setMessage(JSON.toJSONString(result));
        remoteManager.pushCommon(push);

        //指令原路返回
        String[] s  = serialNumber.split("_");
        if (s.length > 1){
            serialNumber = s[0];
        }
        PushMessageBo messageBo = new PushMessageBo();
        JSONArray array = new JSONArray();
        String upTopic = topicsUtils.buildTopic(productId, serialNumber, TopicType.WS_SERVICE_INVOKE_SIMULATE);
        MqttBo receive = new MqttBo();
        receive.setTopic(upTopic);
        receive.setData(ByteBufUtil.hexDump(source));
        receive.setDirection("receive");
        array.add(receive);
        messageBo.setMessage(array.toJSONString());
        messageBo.setTopic(upTopic);
        remoteManager.pushCommon(messageBo);

    }


    /**
     * 模拟设备数据更新
     */
    public void updateSimulationLog(MqttBo send ,MqttBo receive,String serialNumber){
        SimulateLog log = new SimulateLog();
        log.setSendData(JSON.toJSONString(send));
        log.setCallbackData(JSON.toJSONString(receive));
        log.setSerialNumber(serialNumber);
        log.setCreateTime(DateUtils.getNowDate());
        simulateLogService.insertSimulateLog(log);
    }

    /**
     * 发送模拟设备到WS
     */
    public void sendSimulationWs(MqttBo send ,MqttBo receive,String topic){
        PushMessageBo messageBo = new PushMessageBo();
        messageBo.setTopic(topic);
        JSONArray array = new JSONArray();
        send.setDirection("send");
        send.setTs(DateUtils.getNowDate());
        receive.setTs(DateUtils.getNowDate());
        receive.setDirection("receive");
        array.add(send);
        array.add(receive);
        messageBo.setMessage(array.toJSONString());
        remoteManager.pushCommon(messageBo);
    }

    public byte[] CRC(byte[] source) {
        source[2] = (byte)((int) source[2] * 2);
        byte[] result = new byte[source.length + 2];
        byte[] crc16Byte = CRC16Utils.getCrc16Byte(source);
        System.arraycopy(source, 0, result, 0, source.length);
        System.arraycopy(crc16Byte, 0, result, result.length - 2, 2);
        return result;
    }


    /**
     * 搭建消息
     *
     * @param bo
     * @return
     */
    private DeviceDownMessage buildMessage(OtaUpgradeBo bo) {
        String messageId = String.valueOf(snowflakeIdWorker.nextId());
        bo.setMessageId(messageId);
        bo.setOtaUrl("http://" + IpUtils.getHostIp()+bo.getOtaUrl());
        return DeviceDownMessage.builder()
                .productId(bo.getProductId())
                .serialNumber(bo.getSerialNumber())
                .body(JSON.toJSON(bo))
                .timestamp(DateUtils.getTimestamp())
                .messageId(messageId)
                .build();

    }

}

package com.fastbee.common.core.mq.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 影子模式消息数据结构
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShadowMessage {
    
    /**
     * 消息ID，用于标识唯一消息
     */
    private Long id;
    
    /**
     * 设备ID，通常为设备序列号
     */
    private String deviceId;
    
    /**
     * 影子数据，键值对格式
     * key: 物模型标识符
     * value: 影子值
     */
    private Map<String, Object> data;
}

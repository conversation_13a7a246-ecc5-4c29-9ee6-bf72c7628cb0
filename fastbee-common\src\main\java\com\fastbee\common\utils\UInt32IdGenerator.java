package com.fastbee.common.utils;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 32位无符号整数ID生成器
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class UInt32IdGenerator {
    
    /**
     * 使用AtomicLong来确保线程安全，但只使用低32位
     */
    private static final AtomicLong counter = new AtomicLong(0);
    
    /**
     * 32位无符号整数的最大值 (2^32 - 1)
     */
    private static final long UINT32_MAX = 0xFFFFFFFFL;
    
    /**
     * 生成32位无符号整数ID
     * 
     * @return 32位无符号整数，在Java中表示为Integer
     */
    public static Integer generateId() {
        long nextValue = counter.incrementAndGet();
        
        // 确保值在32位无符号整数范围内 (0 到 4294967295)
        long uint32Value = nextValue & UINT32_MAX;
        
        // 如果达到最大值，重置计数器
        if (uint32Value == UINT32_MAX) {
            counter.set(0);
            return 0;
        }
        
        // 将无符号32位值转换为Java的有符号Integer
        // 对于大于Integer.MAX_VALUE的值，会变成负数，但在JSON序列化时会正确显示
        return (int) uint32Value;
    }
    
    /**
     * 基于时间戳生成32位无符号整数ID
     * 使用当前时间戳的低32位
     * 
     * @return 32位无符号整数
     */
    public static Integer generateTimeBasedId() {
        long timestamp = System.currentTimeMillis();
        return (int) (timestamp & UINT32_MAX);
    }
    
    /**
     * 将Java的Integer转换为无符号32位整数的长整型表示
     * 用于日志输出或调试
     * 
     * @param signedInt Java的有符号Integer
     * @return 对应的无符号32位整数值
     */
    public static long toUnsignedLong(Integer signedInt) {
        return signedInt & UINT32_MAX;
    }
}

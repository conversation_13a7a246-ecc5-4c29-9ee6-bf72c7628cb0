# 项目相关配置
fastbee:
  name: fastbee             # 名称
  version: 3.8.5            # 版本
  copyrightYear: 2023       # 版权年份
  demoEnabled: true         # 实例演示开关
  # 文件路径，以uploadPath结尾 示例（ Windows配置 D:/uploadPath，Linux配置 /uploadPath）
  # profile: "D:/uploadPath"
  addressEnabled: true      # 获取ip地址开关
  captchaType: math         # 验证码类型 math 数组计算 char 字符验证

# 开发环境配置
server:
  port: 5080                # 服务器的HTTP端口，默认为8080
  servlet:
    context-path: /         # 应用的访问路径
  tomcat:
    uri-encoding: UTF-8     # tomcat的URI编码
    accept-count: 1000      # 连接数满后的排队数，默认为100
    threads:
      max: 800              # tomcat最大线程数，默认为200
      min-spare: 100        # Tomcat启动初始化的线程数，默认值10
  # 基于netty的服务器
  broker:
    enabled: false           # mqttBroker类型选择, true: 基于netty的mqttBroker和webSocket  false: emq的mqttBroker
    broker-node: node1       # 服务器集群节点
    port: 1883
    openws: true             # 控制webSocket是否开启
    websocket-port: 5081
    websocket-path: /mqtt
    keep-alive: 70          # 默认的全部客户端心跳上传时间
  wx:
    enabled: true   # 启用小程序WebSocket服务
    port: 5085      # 小程序WebSocket端口
    path: /infosocket       # 小程序WebSocket路径
    keep-alive: 60

  #TCP服务端口
  tcp:
    enabled: true          # 控制tcp端口是否开启
    port: 5082
    keep-alive: 70
    delimiter: 0x7e
  udp:
    enabled: false          # 控制udp端口是否开启
    port: 5083
    read-idle: 300          # udp保活时间 默认5分钟
  #平台判断离线时间 是心跳时间的2倍
  device:
    platform:
      expried: 120
    # 数据变化检测配置
    data-change:
      enabled: true                    # 是否启用数据变化检测
      unchanged-threshold: 10          # 数据未变化时的存储阈值（多少次未变化后强制存储一次）
      expire-seconds: 3600             # 数据变化检测的过期时间（秒）

# Spring配置
spring:
  # 环境配置，dev=开发环境，prod=生产环境
  profiles:
    active: dev               # 环境配置，dev=开发环境，prod=生产环境
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      max-file-size:  10MB                             # 单个文件大小
      max-request-size:  20MB                          # 设置总上传的文件大小
  # 服务模块
  devtools:
    restart:
      enabled: true                                    # 热部署开关
  task:
    execution:
      pool:
        core-size: 20                                  # 最小连接数
        max-size: 200                                  # 最大连接数
        queue-capacity: 3000                           # 最大容量
        keep-alive: 60
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
    # 当前时区
    locale: zh
    # 设置全局时区
    time-zone: GMT+8

#集群配置
cluster:
  enable: true
  type: redis


# 用户配置
user:
  password:
    maxRetryCount: 5                                    # 密码最大错误次数
    lockTime: 10                                        # 密码锁定时间（默认10分钟）

# token配置
token:
  header: Authorization                                 # 令牌自定义标识
  secret: abcdefghijklfastbeesmartrstuvwxyz             # 令牌密钥
  expireTime: 1440                                      # 令牌有效期（默认30分钟）1440为一天

# MyBatis配置
#mybatis:
#  typeAliasesPackage: com.fastbee.**.domain             # 搜索指定包别名
#  mapperLocations: classpath*:mapper/**/*Mapper.xml     # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  configLocation: classpath:mybatis/mybatis-config.xml  # 加载全局的配置文件

# mybatis-plus配置
mybatis-plus:
  typeAliasesPackage: com.fastbee.**.domain, com.basic.**.domain             # 搜索指定包别名
  mapperLocations: classpath*:mapper/**/*Mapper.xml     # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  configLocation: classpath:mybatis/mybatis-config.xml  # 加载全局的配置文件
  global-config:
    db-config:
      #主键类型  AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID;
      id-type: ASSIGN_ID
      logic-delete-value: 2 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  configuration:
#    打印sql
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    jdbc-type-for-null: 'null'
    #数据库大写下划线转换
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
    cache-enabled: false
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  enabled: true                                         # 过滤开关
  excludes: /system/notice,/system/sysTask              # 排除链接（多个用逗号分隔）
  urlPatterns: /system/*,/monitor/*,/tool/*             # 匹配链接

#积木报表配置项(可忽略)
minidao :
  base-package: org.jeecg.modules.jmreport.desreport.dao*

# Flowable 配置
flowable:
  # 关闭定时任务 job  act_ru_job数据不匹配会导致报错(不影响运行)
  async-executor-activate: false
  # 库与数据库表结构不一致时，会自动将数据库表结构升级至新版本。
  database-schema-update: false
  common:
    app:
      idm-admin:
        password: test
        user: test002
      idm-url: http://localhost:8080/flowable-demo
  idm:
    enabled: false

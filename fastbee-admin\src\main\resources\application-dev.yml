# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
#        url: *************************************************************************************************************************************************
#        username: root
#        password: "us@KFL128"
        url: *************************************************************************************************************************************************
        username: root
        password: "fs@SDF123"
      # 从库数据源
      slave:
        enabled: false      # 从数据源开关/默认关闭
        url:
        username:
        password:
      # TDengine数据库
      tdengine-server:
        enabled: false      # 默认不启用TDengine，true=启用，false=不启用
        driverClassName: com.taosdata.jdbc.TSDBDriver
        url: *************************************************************************
        username: root
        password: "zx@fs2024"
        dbName: fastbee_log

      initialSize: 5                        # 初始连接数
      minIdle: 10                           # 最小连接池数量
      maxActive: 20                         # 最大连接池数量
      maxWait: 60000                        # 配置获取连接等待超时的时间
      timeBetweenEvictionRunsMillis: 60000  # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      minEvictableIdleTimeMillis: 300000    # 配置一个连接在池中最小生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000    # 配置一个连接在池中最大生存的时间，单位是毫秒
      validationQuery: SELECT 1 FROM DUAL   # 配置检测连接是否有效
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: fastbee
        login-password: fastbee
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis 配置
  redis:
    host: **********                        # 地址
    port: 5019                              # 端口，默认为6379
    database: 9                             # 数据库索引
    password: fs@FSD123                       # 密码
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0                         # 连接池中的最小空闲连接
        max-idle: 8                         # 连接池中的最大空闲连接
        max-active: 8                       # 连接池的最大数据库连接数
        max-wait: -1ms                      # 连接池最大阻塞等待时间（使用负值表示没有限制）
  # mqtt 配置
  mqtt:
    username: gmegUser                       # 账号
    password: "sFidk@9d800lk"                       # 密码
    host-url: tcp://**********:1883          # mqtt连接tcp地址
    client-id: ${random.value}                # 客户端Id，不能相同，采用随机数 ${random.value}
    default-topic: "test"                     # 默认主题
    timeout: 30                             # 超时时间
    keepalive: 30                           # 保持连接
    clearSession: true                      # 清除会话(设置为false,断开连接，重连后使用原来的会话 保留订阅的主题，能接收离线期间的消息)

# sip 配置
sip:
  enabled: true                            # 是否启用视频监控SIP，true为启用
  ## 本地调试时，绑定网卡局域网IP，设备在同一局域网，设备接入IP填写绑定IP
  ## 部署服务端时，默认绑定容器IP，设备接入IP填写服务器公网IP
  ip: 127.0.0.1
  port: 5061                                # SIP端口(保持默认)
  domain: 3402000000                        # 由省级、市级、区级、基层编号组成
  id: 34020000002000000001                  # 同上，另外增加编号，(可保持默认)
  password: "fs@#3348923"                        # 监控设备接入的密码

# 日志配置
logging:
  level:
    com.fastbee: error
    com.yomahub: error
    org.dromara: error
    org.springframework: error

# Swagger配置
swagger:
  enabled: true                             # 是否开启swagger
  pathMapping: /prod-api                    # 请求前缀


liteflow:
  #FlowExecutor的execute2Future的线程数
  main-executor-works: 128
  #FlowExecutor的execute2Future的自定义线程池Builder
  main-executor-class: com.fastbee.mq.ruleEngine.MainExecutorBuilder
  #并行节点的线程池Builder
  thread-executor-class: com.fastbee.mq.ruleEngine.WhenExecutorBuilder
  rule-source-ext-data-map:
    # 应用名称，规则链和脚本组件名称需要一致，不要修改
    applicationName: fastbee
    #是否开启SQL日志
    sqlLogEnabled: true
    # 规则多时，启用快速加载模式
    fast-load: false
    #是否开启SQL数据轮询自动刷新机制 默认不开启
    pollingEnabled: false
    pollingIntervalSeconds: 60
    pollingStartSeconds: 60
    #以下是chain表的配置
    chainTableName: iot_scene
    chainApplicationNameField: application_name
    chainNameField: chain_name
    elDataField: el_data
    chainEnableField: enable
    #以下是script表的配置
    scriptTableName: iot_script
    scriptApplicationNameField: application_name
    scriptIdField: script_id
    scriptNameField: script_name
    scriptDataField: script_data
    scriptTypeField: script_type
    scriptLanguageField: script_language
    scriptEnableField: enable

# 项目相关配置
fastbee:
  # 文件路径，以uploadPath结尾 示例（ Windows配置 D:/uploadPath，Linux配置 /uploadPath）
  profile: "D:/uploadPath"

#积木报表配置
jeecg :
  jmreport:
    #多租户模式，默认值为空(created:按照创建人隔离、tenant:按照租户隔离) (v1.6.2+ 新增)
    saasMode: null
    # 平台上线安全配置(v1.6.2+ 新增)
    firewall:
      # 数据源安全 (开启后，不允许使用平台数据源、SQL解析加签并且不允许查询数据库)
      dataSourceSafe: false
      # 低代码开发模式（dev:开发模式, prod:发布模式关闭报表设计,admin或预留角色可设计, prodsf:发布安全模式 彻底关闭报表设计）
      # lowCodeMode设置为prod或prodsf时，系统将禁止向内网ip地址发送网络请求。
      lowCodeMode: dev
        # api数据集内网ip白名单，在发布模式下只只能向设置了白名单的内网服务器发送请求。
        # 建议：为确保生产环境安全，建议不要在积木报表中通过内网ip调用内网服务。
        #apiDsIpWhite:
        #- ************
        #- ***********
      #- *********/24
    #是否 禁用导出PDF和图片的按钮 默认为false
    exportDisabled: false
    # 导出是否开启另存为弹窗 默认为false (v1.7.4+)
    enableSaveAsOpen: false
    #是否自动保存
    autoSave: true
    #自动保存间隔时间毫秒
    interval: 20000
    # 行数(设计页面展示多少行)
    row: 100
    # 列数(设计页面展示多少列)
    col: 100
    #自定义项目前缀
    #customPrePath:
    # 自定义API接口的前缀 #{api_base_path}和#{domainURL}的值
    #apiBasePath: http://localhost:8080/jeecg-boot
    #预览分页自定义
    pageSize:
      - 10
      - 20
      - 30
      - 40
    #打印纸张自定义
    printPaper:
      - title: A5纸
        size:
          - 148
          - 210
      - title: B4纸
        size:
          - 250
          - 353
    #接口超时设置（毫秒）
    connect-timeout: 300000
    #Excel导出模式(fast/快、primary/精致模式，默认fast)
    export-excel-pattern: fast
    #Excel导出数据每个sheet的行数,每个sheet最大1048576行
    page-size-number: 10000
    #设计页面表格的线是否显示 默认true
    line: true
